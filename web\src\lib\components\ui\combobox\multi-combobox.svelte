<script lang="ts">
  import { Check, ChevronDown } from 'lucide-svelte';
  import * as Popover from '$lib/components/ui/popover';
  import { Button } from '$lib/components/ui/button';
  import { tick, onMount, onDestroy, createEventDispatcher } from 'svelte';
  import { cn } from '$lib/utils';
  import { browser } from '$app/environment'; // Kept for API compatibility
  import { activeDropdownId } from '$lib/stores/dropdown-store';
  import * as Command from '../command';

  // Create event dispatcher for Svelte 5
  const dispatch = createEventDispatcher<{
    change: { selectedValues: string[] };
    search: { searchValue: string };
  }>();

  // Props that can be passed from parent
  const {
    options = [],
    selectedValues: initialValues = [],
    placeholder = 'Select items...',
    searchPlaceholder = 'Search...',
    emptyMessage = 'No items found.',
    width = 'w-[200px]',
    disabled = false,
    paramName = '', // URL parameter name (handled by parent component)
    maxDisplayItems = 2, // Maximum number of items to display in the button
    onSelectedValuesChange = undefined, // Callback for when selected values change
    searchOptions = function () {
      return Promise.resolve([]);
    }, // Callback for external search
  } = $props<{
    options: { value: string; label: string }[];
    selectedValues?: string[];
    placeholder?: string;
    searchPlaceholder?: string;
    emptyMessage?: string;
    width?: string;
    disabled?: boolean;
    paramName?: string;
    maxDisplayItems?: number;
    onSelectedValuesChange?: (values: string[]) => void;
    searchOptions?: (searchValue: string) => Promise<{ value: string; label: string }[]>;
  }>();

  // Use state for selected values
  let selectedValues = $state<string[]>([...initialValues]);

  // State variables
  let open = $state(false);
  let filteredOptions = $state<typeof options>([]);
  let searchValue = $state('');
  let highlightedIndex = $state(-1); // For keyboard navigation
  let triggerRef = $state<HTMLButtonElement | null>(null);
  let isSearching = $state(false);

  // Generate a unique ID for this dropdown
  const dropdownId = `multi-combobox-${Math.random().toString(36).substring(2, 9)}`;

  // Subscribe to the active dropdown store
  let unsubscribe: (() => void) | null = null;

  // Methods to get and set selected values programmatically
  export function setSelectedValues(values: string[]) {
    selectedValues = values;
    dispatchChange();
  }

  export function getSelectedValues(): string[] {
    return [...selectedValues];
  }

  // Get selected labels for display
  const selectedLabels = $derived(
    selectedValues.map(
      (value: string) =>
        options.find((option: { value: string; label: string }) => option.value === value)?.label ||
        value
    )
  );

  function dispatchChange() {
    // Update URL parameter if paramName is provided
    if (paramName && browser) {
      console.log(`MultiCombobox: Updating URL param ${paramName} with values:`, selectedValues);
      updateUrlParam();
    } else if (browser) {
      console.log(`MultiCombobox: No paramName provided, skipping URL update`);
    }

    // Dispatch change event
    dispatch('change', { selectedValues });

    // Call the callback directly if provided
    if (onSelectedValuesChange) {
      console.log(`MultiCombobox: Calling onSelectedValuesChange with values:`, selectedValues);
      onSelectedValuesChange(selectedValues);
    }
  }

  // Update URL parameter without page reload
  function updateUrlParam() {
    if (!browser || !paramName) return;

    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);

    if (selectedValues.length > 0) {
      // Filter out any empty values before joining
      const filteredValues = selectedValues.filter(Boolean);
      if (filteredValues.length > 0) {
        // For arrays, we need to preserve the exact values without additional URL encoding
        // We'll use a custom encoding approach to ensure complex IDs are preserved
        const encodedValue = filteredValues.map((v: string) => encodeURIComponent(v)).join(',');
        params.set(paramName, encodedValue);
        console.log(`MultiCombobox: Setting URL param ${paramName} to ${encodedValue}`);
      } else {
        params.delete(paramName);
        console.log(`MultiCombobox: Deleting URL param ${paramName} (empty filtered values)`);
      }
    } else {
      params.delete(paramName);
      console.log(`MultiCombobox: Deleting URL param ${paramName} (no selected values)`);
    }

    // Preserve all other URL parameters
    const newUrl = `${url.pathname}?${params.toString()}`;
    console.log(`MultiCombobox: New URL: ${newUrl}`);
    window.history.replaceState({}, '', newUrl);
  }

  // Handle search as a direct promise without timeout
  async function dispatchSearch(value: string) {
    // Set searching state immediately
    isSearching = true;

    searchOptions(value)
      .then((searchResults) => {
        if (Array.isArray(searchResults)) {
          filteredOptions = [...searchResults];
          console.log(filteredOptions);
        } else {
          console.warn('searchOptions did not return an array:', searchResults);
          // Fall back to local filtering
          filteredOptions = value
            ? [
                ...options.filter((option: { value: string; label: string }) =>
                  option.label.toLowerCase().includes(value.toLowerCase())
                ),
              ]
            : [...options];
        }
      })
      .catch((error) => {
        console.error('Error in searchOptions:', error);
        // Fall back to local filtering
        filteredOptions = value
          ? [
              ...options.filter((option: { value: string; label: string }) =>
                option.label.toLowerCase().includes(value.toLowerCase())
              ),
            ]
          : [...options];
      })
      .finally(() => {
        dispatch('search', { searchValue: value });

        // Clear searching state
        isSearching = false;
        console.log(isSearching);
      });
  }

  function toggleItem(value: string) {
    if (selectedValues.includes(value)) {
      selectedValues = selectedValues.filter((v: string) => v !== value);
    } else {
      selectedValues = [...selectedValues, value];
    }
    dispatchChange();
  }

  function clearAll() {
    selectedValues = [];
    dispatchChange();
  }

  // Close popover and focus trigger button
  function closeAndFocusTrigger() {
    open = false;
    highlightedIndex = -1; // Reset highlighted index when closing
    tick().then(() => {
      if (triggerRef) {
        triggerRef.focus();
      }
    });
  }

  // Handle keyboard navigation
  function handleKeyDown(event: KeyboardEvent) {
    if (!open) return;

    // Handle Alt+C for clearing selections
    if (event.altKey && event.key === 'c') {
      event.preventDefault();
      if (selectedValues.length > 0) {
        clearAll();
        closeAndFocusTrigger();
      }
      return;
    }

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        highlightedIndex = Math.min(highlightedIndex + 1, filteredOptions.length - 1);
        break;
      case 'ArrowUp':
        event.preventDefault();
        highlightedIndex = Math.max(highlightedIndex - 1, -1);
        break;
      case 'Enter':
        event.preventDefault();
        if (highlightedIndex >= 0 && highlightedIndex < filteredOptions.length) {
          toggleItem(filteredOptions[highlightedIndex].value);
        }
        break;
      case 'Escape':
        event.preventDefault();
        closeAndFocusTrigger();
        break;
      case 'Tab':
        if (!event.shiftKey) {
          // Allow natural tabbing to the next element
          closeAndFocusTrigger();
        }
        break;
    }
  }

  // Initialize component
  onMount(() => {
    // Initialize filtered options
    filteredOptions = options;

    // Subscribe to the active dropdown store
    unsubscribe = activeDropdownId.subscribe((activeId) => {
      // If another dropdown is active and this one is open, close it
      if (activeId && activeId !== dropdownId && open) {
        open = false;
      }
    });
  });

  // Clean up on component destruction
  onDestroy(() => {
    if (unsubscribe) {
      unsubscribe();
    }
  });

  // Handle open state changes
  function handleOpenChange(newOpenState: boolean) {
    open = newOpenState;

    if (newOpenState) {
      // Reset highlighted index when opening
      highlightedIndex = -1;

      // Register this dropdown as the active one
      activeDropdownId.set(dropdownId);
    } else {
      // Clear this dropdown from being active
      activeDropdownId.update((currentId) => (currentId === dropdownId ? null : currentId));
    }
  }
</script>

<Popover.Root bind:open onopenchange={(e: CustomEvent<boolean>) => handleOpenChange(e.detail)}>
  <Popover.Trigger bind:ref={triggerRef} class="!overflow-hidden">
    {#snippet child({ props })}
      <Button
        variant="outline"
        role="combobox"
        aria-expanded={open}
        aria-haspopup="listbox"
        aria-controls="multi-combobox-options"
        aria-label={placeholder}
        {disabled}
        {...props}>
        <div class="flex flex-1 flex-wrap items-center gap-1 overflow-hidden">
          {#if selectedValues.length === 0}
            <span class="text-muted-foreground">{placeholder}</span>
          {:else if selectedValues.length <= maxDisplayItems}
            {#each selectedLabels as label, i}
              <span class="truncate">{label}{i < selectedLabels.length - 1 ? ', ' : ''}</span>
            {/each}
          {:else}
            {#each selectedLabels.slice(0, maxDisplayItems) as label, i}
              <span class="truncate">{label}{i < maxDisplayItems - 1 ? ', ' : ''}</span>
            {/each}
            <span class="text-muted-foreground ml-1 text-xs"
              >+{selectedValues.length - maxDisplayItems} more</span>
          {/if}
        </div>

        <ChevronDown class="ml-2 size-4 opacity-50" />
      </Button>
    {/snippet}
  </Popover.Trigger>

  <Popover.Content class="{width} rounded-none p-0" align="start" sideOffset={8}>
    <Command.Root options={filteredOptions} shouldFilter={false}>
      <Command.Input
        placeholder={searchPlaceholder}
        bind:value={searchValue}
        oninput={() => dispatchSearch(searchValue)}
        onkeydown={handleKeyDown} />
      <Command.List class="py-1">
        {#if isSearching}
          <Command.Empty class="p-2">Searching...</Command.Empty>
        {:else if filteredOptions.length === 0}
          <Command.Empty class="p-2">No results found.</Command.Empty>
        {/if}
      </Command.List>
      {#if selectedValues.length > 0}
        <div class="border-t px-2">
          <Button
            type="button"
            size="sm"
            variant="ghost"
            class="my-2 h-6 w-full rounded-none p-1 text-center text-xs"
            aria-label="Clear all selections"
            onclick={() => {
              clearAll();
              closeAndFocusTrigger();
            }}
            onkeydown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                clearAll();
                closeAndFocusTrigger();
              }
            }}>
            Clear selections
          </Button>
        </div>
      {/if}
    </Command.Root>
  </Popover.Content>
</Popover.Root>
